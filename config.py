"""
配置文件 - 创业分析智能体系统
"""
import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # 数据库配置
    SQL_SERVER_HOST = os.getenv("SQL_SERVER_HOST", "localhost")
    SQL_SERVER_PORT = os.getenv("SQL_SERVER_PORT", "1433")
    SQL_SERVER_DATABASE = os.getenv("SQL_SERVER_DATABASE", "HurunDB")
    SQL_SERVER_USERNAME = os.getenv("SQL_SERVER_USERNAME", "")
    SQL_SERVER_PASSWORD = os.getenv("SQL_SERVER_PASSWORD", "")
    
    # 通义千问API配置
    DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY", "")
    
    # 模型配置
    EMBEDDING_MODEL = "text-embedding-v4"  # 通义千问embedding模型
    RERANK_MODEL = "gte-rerank"  # 通义千问rerank模型
    LLM_MODEL = "qwen-plus"  # 主要对话模型
    
    # RAG配置
    PARENT_CHUNK_SIZE = 1024  # 父块最大长度
    CHILD_CHUNK_SIZE = 512    # 子块最大长度
    CHUNK_OVERLAP = 100       # 分块重叠长度
    TOP_K_RETRIEVAL = 10      # 检索返回的文档数量
    RERANK_TOP_K = 5          # 重排后保留的文档数量
    
    # 向量数据库配置
    VECTOR_DB_PATH = "./vector_db"
    COLLECTION_NAME = "hurun_knowledge"
    
    # 系统配置
    MAX_TOKENS = 4000
    TEMPERATURE = 0.7
    
    # 行业配置
    SUPPORTED_INDUSTRIES = [
        "科技互联网",
        "新能源",
        "生物医药",
        "消费品牌",
        "金融科技",
        "教育培训",
        "文娱传媒",
        "智能制造",
        "新零售",
        "企业服务"
    ]
    
    # 智能体人格配置
    AGENT_PERSONALITY = {
        "style": "亲切幽默",
        "tone": "专业而温暖",
        "target_audience": "U30创业者",
        "expertise_level": "资深行业专家"
    }

# 创建全局配置实例
config = Config()
