"""
数据库连接模块
负责与SQL Server HurunDB的连接和数据操作
"""
import pyodbc
import pandas as pd
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from loguru import logger
from typing import List, Dict, Any, Optional
from config import config

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.connection_string = self._build_connection_string()
        self.engine = None
        self._connect()
    
    def _build_connection_string(self) -> str:
        """构建数据库连接字符串"""
        return (
            f"mssql+pyodbc://{config.SQL_SERVER_USERNAME}:"
            f"{config.SQL_SERVER_PASSWORD}@{config.SQL_SERVER_HOST}:"
            f"{config.SQL_SERVER_PORT}/{config.SQL_SERVER_DATABASE}"
            f"?driver=ODBC+Driver+17+for+SQL+Server"
        )
    
    def _connect(self):
        """建立数据库连接"""
        try:
            self.engine = create_engine(self.connection_string)
            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("数据库连接成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def execute_query(self, query: str, params: Dict = None) -> pd.DataFrame:
        """执行查询并返回DataFrame"""
        try:
            with self.engine.connect() as conn:
                result = pd.read_sql(text(query), conn, params=params)
            logger.info(f"查询执行成功，返回 {len(result)} 行数据")
            return result
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            raise
    
    def get_table_info(self, table_name: str = None) -> pd.DataFrame:
        """获取表结构信息"""
        if table_name:
            query = """
            SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = :table_name
            ORDER BY ORDINAL_POSITION
            """
            return self.execute_query(query, {"table_name": table_name})
        else:
            query = """
            SELECT 
                TABLE_NAME,
                COUNT(*) as COLUMN_COUNT
            FROM INFORMATION_SCHEMA.COLUMNS 
            GROUP BY TABLE_NAME
            ORDER BY TABLE_NAME
            """
            return self.execute_query(query)
    
    def get_all_data_for_rag(self) -> List[Dict[str, Any]]:
        """获取所有用于RAG的数据"""
        try:
            # 获取所有表名
            tables_df = self.get_table_info()
            all_data = []
            
            for table_name in tables_df['TABLE_NAME'].unique():
                logger.info(f"正在提取表 {table_name} 的数据...")
                
                # 获取表数据
                query = f"SELECT * FROM {table_name}"
                table_data = self.execute_query(query)
                
                # 将每行数据转换为文档格式
                for _, row in table_data.iterrows():
                    doc = {
                        "source_table": table_name,
                        "content": self._row_to_text(row, table_name),
                        "metadata": {
                            "table": table_name,
                            "row_id": getattr(row, 'id', None),
                            "data_type": "database_record"
                        }
                    }
                    all_data.append(doc)
            
            logger.info(f"总共提取了 {len(all_data)} 条记录用于RAG")
            return all_data
            
        except Exception as e:
            logger.error(f"数据提取失败: {e}")
            raise
    
    def _row_to_text(self, row: pd.Series, table_name: str) -> str:
        """将数据行转换为文本格式"""
        text_parts = [f"表名: {table_name}"]
        
        for column, value in row.items():
            if pd.notna(value):
                text_parts.append(f"{column}: {value}")
        
        return " | ".join(text_parts)
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logger.info("数据库连接已关闭")

# 创建全局数据库管理器实例
db_manager = DatabaseManager()
