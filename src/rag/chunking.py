"""
文档分块模块
实现父子分段策略，父块1024字符，子块512字符
"""
from typing import List, Dict, Any, Tuple
import re
from loguru import logger
from config import config

class ParentChildChunker:
    """父子分块器"""
    
    def __init__(self, 
                 parent_chunk_size: int = config.PARENT_CHUNK_SIZE,
                 child_chunk_size: int = config.CHILD_CHUNK_SIZE,
                 overlap: int = config.CHUNK_OVERLAP):
        self.parent_chunk_size = parent_chunk_size
        self.child_chunk_size = child_chunk_size
        self.overlap = overlap
    
    def chunk_documents(self, documents: List[Dict[str, Any]]) -> Tuple[List[Dict], List[Dict]]:
        """
        对文档进行父子分块
        返回: (parent_chunks, child_chunks)
        """
        parent_chunks = []
        child_chunks = []
        
        for doc_idx, document in enumerate(documents):
            content = document.get('content', '')
            metadata = document.get('metadata', {})
            
            # 生成父块
            parent_texts = self._split_text(content, self.parent_chunk_size, self.overlap)
            
            for parent_idx, parent_text in enumerate(parent_texts):
                parent_id = f"parent_{doc_idx}_{parent_idx}"
                
                parent_chunk = {
                    'id': parent_id,
                    'content': parent_text,
                    'metadata': {
                        **metadata,
                        'chunk_type': 'parent',
                        'doc_index': doc_idx,
                        'parent_index': parent_idx,
                        'source_table': document.get('source_table', ''),
                    }
                }
                parent_chunks.append(parent_chunk)
                
                # 为每个父块生成子块
                child_texts = self._split_text(parent_text, self.child_chunk_size, self.overlap // 2)
                
                for child_idx, child_text in enumerate(child_texts):
                    child_id = f"child_{doc_idx}_{parent_idx}_{child_idx}"
                    
                    child_chunk = {
                        'id': child_id,
                        'content': child_text,
                        'metadata': {
                            **metadata,
                            'chunk_type': 'child',
                            'doc_index': doc_idx,
                            'parent_index': parent_idx,
                            'child_index': child_idx,
                            'parent_id': parent_id,
                            'source_table': document.get('source_table', ''),
                        }
                    }
                    child_chunks.append(child_chunk)
        
        logger.info(f"生成了 {len(parent_chunks)} 个父块和 {len(child_chunks)} 个子块")
        return parent_chunks, child_chunks
    
    def _split_text(self, text: str, chunk_size: int, overlap: int) -> List[str]:
        """按指定大小分割文本"""
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # 如果不是最后一块，尝试在句号、感叹号、问号处断开
            if end < len(text):
                # 寻找最近的句子结束符
                sentence_end = max(
                    text.rfind('。', start, end),
                    text.rfind('！', start, end),
                    text.rfind('？', start, end),
                    text.rfind('.', start, end),
                    text.rfind('!', start, end),
                    text.rfind('?', start, end)
                )
                
                if sentence_end > start:
                    end = sentence_end + 1
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # 计算下一个开始位置，考虑重叠
            start = end - overlap if end < len(text) else len(text)
        
        return chunks
    
    def get_parent_chunk(self, child_chunk_id: str, parent_chunks: List[Dict]) -> Dict:
        """根据子块ID获取对应的父块"""
        # 从子块ID中提取父块ID
        parts = child_chunk_id.split('_')
        if len(parts) >= 4:
            parent_id = f"parent_{parts[1]}_{parts[2]}"
            
            for parent_chunk in parent_chunks:
                if parent_chunk['id'] == parent_id:
                    return parent_chunk
        
        return None

# 创建全局分块器实例
chunker = ParentChildChunker()
