"""
嵌入模型模块
使用通义千问的text-embedding-v4模型进行文本向量化
"""
import dashscope
from dashscope import TextEmbedding
from typing import List, Union
import numpy as np
from loguru import logger
from config import config

class QwenEmbeddings:
    """通义千问嵌入模型封装"""
    
    def __init__(self):
        dashscope.api_key = config.DASHSCOPE_API_KEY
        self.model_name = config.EMBEDDING_MODEL
        
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """对文档列表进行嵌入"""
        try:
            embeddings = []
            batch_size = 10  # 批处理大小
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                
                response = TextEmbedding.call(
                    model=self.model_name,
                    input=batch_texts
                )
                
                if response.status_code == 200:
                    batch_embeddings = [item['embedding'] for item in response.output['embeddings']]
                    embeddings.extend(batch_embeddings)
                else:
                    logger.error(f"嵌入生成失败: {response}")
                    raise Exception(f"嵌入生成失败: {response}")
            
            logger.info(f"成功生成 {len(embeddings)} 个文档的嵌入向量")
            return embeddings
            
        except Exception as e:
            logger.error(f"文档嵌入失败: {e}")
            raise
    
    def embed_query(self, text: str) -> List[float]:
        """对查询文本进行嵌入"""
        try:
            response = TextEmbedding.call(
                model=self.model_name,
                input=[text]
            )
            
            if response.status_code == 200:
                embedding = response.output['embeddings'][0]['embedding']
                logger.debug(f"查询嵌入生成成功，维度: {len(embedding)}")
                return embedding
            else:
                logger.error(f"查询嵌入失败: {response}")
                raise Exception(f"查询嵌入失败: {response}")
                
        except Exception as e:
            logger.error(f"查询嵌入失败: {e}")
            raise
    
    def get_embedding_dimension(self) -> int:
        """获取嵌入向量维度"""
        # text-embedding-v4 的维度是 1024
        return 1024

# 创建全局嵌入模型实例
qwen_embeddings = QwenEmbeddings()
