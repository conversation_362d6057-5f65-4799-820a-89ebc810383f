"""
向量存储模块
使用ChromaDB作为向量数据库，实现混合检索
"""
import chromadb
from chromadb.config import Settings
import os
from typing import List, Dict, Any, Tuple
import json
from loguru import logger
from config import config
from .embeddings import qwen_embeddings

class VectorStore:
    """向量存储管理器"""
    
    def __init__(self):
        self.client = None
        self.parent_collection = None
        self.child_collection = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化ChromaDB客户端"""
        try:
            # 确保向量数据库目录存在
            os.makedirs(config.VECTOR_DB_PATH, exist_ok=True)
            
            self.client = chromadb.PersistentClient(
                path=config.VECTOR_DB_PATH,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # 创建或获取集合
            self.parent_collection = self.client.get_or_create_collection(
                name=f"{config.COLLECTION_NAME}_parent",
                metadata={"hnsw:space": "cosine"}
            )
            
            self.child_collection = self.client.get_or_create_collection(
                name=f"{config.COLLECTION_NAME}_child",
                metadata={"hnsw:space": "cosine"}
            )
            
            logger.info("向量数据库初始化成功")
            
        except Exception as e:
            logger.error(f"向量数据库初始化失败: {e}")
            raise
    
    def add_documents(self, parent_chunks: List[Dict], child_chunks: List[Dict]):
        """添加文档到向量数据库"""
        try:
            # 添加父块
            if parent_chunks:
                parent_texts = [chunk['content'] for chunk in parent_chunks]
                parent_embeddings = qwen_embeddings.embed_documents(parent_texts)
                
                self.parent_collection.add(
                    embeddings=parent_embeddings,
                    documents=parent_texts,
                    metadatas=[chunk['metadata'] for chunk in parent_chunks],
                    ids=[chunk['id'] for chunk in parent_chunks]
                )
                logger.info(f"添加了 {len(parent_chunks)} 个父块到向量数据库")
            
            # 添加子块
            if child_chunks:
                child_texts = [chunk['content'] for chunk in child_chunks]
                child_embeddings = qwen_embeddings.embed_documents(child_texts)
                
                self.child_collection.add(
                    embeddings=child_embeddings,
                    documents=child_texts,
                    metadatas=[chunk['metadata'] for chunk in child_chunks],
                    ids=[chunk['id'] for chunk in child_chunks]
                )
                logger.info(f"添加了 {len(child_chunks)} 个子块到向量数据库")
                
        except Exception as e:
            logger.error(f"添加文档到向量数据库失败: {e}")
            raise
    
    def hybrid_search(self, query: str, top_k: int = config.TOP_K_RETRIEVAL) -> List[Dict]:
        """
        混合检索：结合父块和子块的检索结果
        """
        try:
            query_embedding = qwen_embeddings.embed_query(query)
            
            # 在子块中搜索（更精确）
            child_results = self.child_collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                include=['documents', 'metadatas', 'distances']
            )
            
            # 在父块中搜索（更全面的上下文）
            parent_results = self.parent_collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k // 2,
                include=['documents', 'metadatas', 'distances']
            )
            
            # 合并结果
            combined_results = []
            
            # 添加子块结果
            for i, doc in enumerate(child_results['documents'][0]):
                combined_results.append({
                    'content': doc,
                    'metadata': child_results['metadatas'][0][i],
                    'score': 1 - child_results['distances'][0][i],  # 转换为相似度分数
                    'source': 'child'
                })
            
            # 添加父块结果
            for i, doc in enumerate(parent_results['documents'][0]):
                combined_results.append({
                    'content': doc,
                    'metadata': parent_results['metadatas'][0][i],
                    'score': 1 - parent_results['distances'][0][i],
                    'source': 'parent'
                })
            
            # 按分数排序
            combined_results.sort(key=lambda x: x['score'], reverse=True)
            
            logger.info(f"混合检索返回 {len(combined_results)} 个结果")
            return combined_results[:top_k]
            
        except Exception as e:
            logger.error(f"混合检索失败: {e}")
            raise
    
    def get_collection_stats(self) -> Dict:
        """获取集合统计信息"""
        try:
            parent_count = self.parent_collection.count()
            child_count = self.child_collection.count()
            
            return {
                'parent_chunks': parent_count,
                'child_chunks': child_count,
                'total_chunks': parent_count + child_count
            }
        except Exception as e:
            logger.error(f"获取集合统计信息失败: {e}")
            return {}
    
    def clear_collections(self):
        """清空所有集合"""
        try:
            self.client.delete_collection(f"{config.COLLECTION_NAME}_parent")
            self.client.delete_collection(f"{config.COLLECTION_NAME}_child")
            self._initialize_client()
            logger.info("向量数据库已清空")
        except Exception as e:
            logger.error(f"清空向量数据库失败: {e}")
            raise

# 创建全局向量存储实例
vector_store = VectorStore()
